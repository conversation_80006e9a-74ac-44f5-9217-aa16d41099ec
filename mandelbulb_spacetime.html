<!DOCTYPE html>
<html>
<head>
    <title>Mandelbulb with Spacetime Grid</title>
    <style>
        body { margin: 0; overflow: hidden; background: #000511; }
        canvas { width: 100vw; height: 100vh; display: block; }
        .controls {
            position: fixed;
            top: 10px;
            left: 10px;
            color: #87CEEB;
            font-family: 'Courier New', monospace;
            background: rgba(0,5,17,0.8);
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #87CEEB;
        }
        .physics-controls {
            position: fixed;
            bottom: 10px;
            left: 10px;
            color: #87CEEB;
            background: rgba(0,5,17,0.8);
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #87CEEB;
        }
        .slider-container {
            margin: 8px 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .slider-container label {
            min-width: 130px;
            font-size: 11px;
        }
        input[type="range"] {
            width: 140px;
        }
        #quality {
            position: fixed;
            top: 10px;
            right: 10px;
            color: #87CEEB;
            background: rgba(0,5,17,0.8);
            padding: 10px;
            border-radius: 8px;
            border: 1px solid #87CEEB;
        }
    </style>
</head>
<body>
<div class="controls">
    <strong>🌌 Spacetime Mandelbulb</strong><br>
    WASD - Move | Mouse - Look | Q/E - Up/Down<br>
    +/- Keys: Adjust Quality
</div>
<div id="quality">Quality: <span id="qualityValue">60</span>%</div>
<div class="physics-controls">
    <div class="slider-container">
        <label>⚡ Gravity Strength:</label>
        <input type="range" id="gravityStrength" min="0" max="100" value="60">
    </div>
    <div class="slider-container">
        <label>🕸️ Grid Density:</label>
        <input type="range" id="gridDensity" min="5" max="50" value="20">
    </div>
    <div class="slider-container">
        <label>🌀 Spacetime Warp:</label>
        <input type="range" id="spacetimeWarp" min="0" max="100" value="75">
    </div>
    <div class="slider-container">
        <label>⭐ Star Field:</label>
        <input type="range" id="starField" min="0" max="100" value="60">
    </div>
    <div class="slider-container">
        <label>🔄 Animation Speed:</label>
        <input type="range" id="animSpeed" min="0" max="100" value="25">
    </div>
</div>
<canvas id="canvas"></canvas>
<script>
const canvas = document.getElementById('canvas');
const gl = canvas.getContext('webgl');

// Camera and quality state
let cameraPos = [0.0, 0.0, -6.0];
let cameraRot = [0.0, 0.0];
let quality = 0.6;

// Physics parameters
let time = 0;
let gravityStrength = 0.6;
let gridDensity = 0.2;
let spacetimeWarp = 0.75;
let starField = 0.6;
let animSpeed = 0.25;

// Update physics parameters from sliders
document.getElementById('gravityStrength').addEventListener('input', (e) => {
    gravityStrength = e.target.value / 100;
});
document.getElementById('gridDensity').addEventListener('input', (e) => {
    gridDensity = e.target.value / 100;
});
document.getElementById('spacetimeWarp').addEventListener('input', (e) => {
    spacetimeWarp = e.target.value / 100;
});
document.getElementById('starField').addEventListener('input', (e) => {
    starField = e.target.value / 100;
});
document.getElementById('animSpeed').addEventListener('input', (e) => {
    animSpeed = e.target.value / 100;
});

// Resize handler
function resize() {
    const width = window.innerWidth * quality;
    const height = window.innerHeight * quality;
    canvas.style.width = window.innerWidth + 'px';
    canvas.style.height = window.innerHeight + 'px';
    canvas.width = width;
    canvas.height = height;
    gl.viewport(0, 0, width, height);
}
window.addEventListener('resize', resize);
resize();

const vertexShaderSource = `
    attribute vec2 position;
    void main() {
        gl_Position = vec4(position, 0.0, 1.0);
    }
`;

const fragmentShaderSource = `
    precision mediump float;
    uniform vec2 resolution;
    uniform vec3 cameraPos;
    uniform vec2 cameraRot;
    uniform float time;
    uniform float gravityStrength;
    uniform float gridDensity;
    uniform float spacetimeWarp;
    uniform float starField;
    uniform float animSpeed;

    // Hash function for noise
    float hash(vec3 p) {
        p = fract(p * 0.3183099 + 0.1);
        p *= 17.0;
        return fract(p.x * p.y * p.z * (p.x + p.y + p.z));
    }

    // Mandelbulb distance function
    float mandelbulb(vec3 pos) {
        vec3 z = pos;
        float dr = 1.0;
        float r = 0.0;
        float power = 8.0 + sin(time * animSpeed) * 2.0;

        for (int i = 0; i < 8; i++) {
            r = length(z);
            if (r > 2.0) break;

            float theta = acos(z.z / r);
            float phi = atan(z.y, z.x);
            dr = pow(r, power - 1.0) * power * dr + 1.0;

            float zr = pow(r, power);
            theta = theta * power;
            phi = phi * power;

            z = zr * vec3(
                sin(theta) * cos(phi),
                sin(theta) * sin(phi),
                cos(theta)
            );
            z += pos;
        }
        return 0.5 * log(r) * r / dr;
    }

    // Gravitational field strength
    float gravitationalField(vec3 pos) {
        float dist = length(pos);
        return gravityStrength / (dist * dist + 0.1);
    }

    // Spacetime grid with warping and animation
    float spacetimeGrid(vec3 pos) {
        // Apply gravitational warping to position
        vec3 warpedPos = pos;
        float field = gravitationalField(pos);
        warpedPos += normalize(pos) * field * spacetimeWarp * 2.0;

        // Add animation - flowing spacetime
        float flowSpeed = time * animSpeed * 3.0;
        warpedPos.x += sin(flowSpeed + pos.z * 0.5) * 0.2;
        warpedPos.y += cos(flowSpeed * 0.7 + pos.x * 0.3) * 0.15;
        warpedPos.z += sin(flowSpeed * 0.5 + pos.y * 0.4) * 0.1;

        // Create animated grid lines with flowing effect
        vec3 animatedPos = warpedPos * gridDensity * 10.0;
        animatedPos += vec3(flowSpeed * 0.5, flowSpeed * 0.3, flowSpeed * 0.4);

        vec3 grid = abs(fract(animatedPos) - 0.5);
        float gridLine = min(min(grid.x, grid.y), grid.z);

        // Make grid lines much thinner and brighter
        gridLine = smoothstep(0.0, 0.005, gridLine);

        return (1.0 - gridLine) * 2.5; // Increased brightness
    }

    // Rotating star field with trails
    float stars(vec3 dir) {
        // Rotate the direction vector around z-axis
        float rotSpeed = time * animSpeed * 0.2;
        float cosR = cos(rotSpeed);
        float sinR = sin(rotSpeed);
        vec3 rotatedDir = vec3(
            dir.x * cosR - dir.y * sinR,
            dir.x * sinR + dir.y * cosR,
            dir.z
        );

        vec3 p = rotatedDir * 100.0;
        float star = hash(floor(p));
        float starBase = step(1.0 - starField * 0.01, star) * smoothstep(0.9, 1.0, star);

        // Add trail effect with multiple samples
        float trail = 0.0;
        for (int i = 1; i <= 5; i++) {
            float trailTime = rotSpeed - float(i) * 0.1;
            float trailCos = cos(trailTime);
            float trailSin = sin(trailTime);
            vec3 trailDir = vec3(
                dir.x * trailCos - dir.y * trailSin,
                dir.x * trailSin + dir.y * trailCos,
                dir.z
            );
            vec3 trailP = trailDir * 100.0;
            float trailStar = hash(floor(trailP));
            float trailIntensity = step(1.0 - starField * 0.01, trailStar) * smoothstep(0.9, 1.0, trailStar);
            trail += trailIntensity * (1.0 - float(i) * 0.15); // Fade out trail
        }

        // Combine main star with trail
        return starBase + trail * 0.3;
    }

    vec3 getRayDir(vec2 uv, vec3 camPos, vec2 camRot) {
        vec3 forward = vec3(
            cos(camRot.y) * sin(camRot.x),
            sin(camRot.y),
            cos(camRot.y) * cos(camRot.x)
        );
        vec3 right = normalize(cross(forward, vec3(0.0, 1.0, 0.0)));
        vec3 up = normalize(cross(right, forward));
        return normalize(forward + uv.x * right + uv.y * up);
    }

    vec3 estimateNormal(vec3 p) {
        float d = 0.01;
        return normalize(vec3(
            mandelbulb(p + vec3(d,0,0)) - mandelbulb(p - vec3(d,0,0)),
            mandelbulb(p + vec3(0,d,0)) - mandelbulb(p - vec3(0,d,0)),
            mandelbulb(p + vec3(0,0,d)) - mandelbulb(p - vec3(0,0,d))
        ));
    }

    void main() {
        vec2 uv = (gl_FragCoord.xy * 2.0 - resolution) / min(resolution.x, resolution.y);
        vec3 rayDir = getRayDir(uv, cameraPos, cameraRot);

        float t = 0.0;
        vec3 col = vec3(0.0);
        bool hitMandelbulb = false;

        // Raymarching for Mandelbulb
        for(int i = 0; i < 60; i++) {
            vec3 pos = cameraPos + rayDir * t;
            float d = mandelbulb(pos);

            if(abs(d) < 0.01) {
                vec3 normal = estimateNormal(pos);
                vec3 light = normalize(vec3(1.0, 1.0, -1.0));
                float diff = max(0.0, dot(normal, light));

                // Light blue Mandelbulb with realistic shading
                vec3 bulbColor = vec3(0.4, 0.7, 1.0);
                col = bulbColor * (diff * 0.8 + 0.2);
                hitMandelbulb = true;
                break;
            }

            t += d;
            if(t > 15.0) break;
        }

        // If we didn't hit the Mandelbulb, render spacetime grid and stars
        if (!hitMandelbulb) {
            // Sample spacetime grid along the ray with more samples for smoother animation
            float gridIntensity = 0.0;
            for (int i = 0; i < 25; i++) {
                float rayT = float(i) * 0.4 + 1.5;
                vec3 samplePos = cameraPos + rayDir * rayT;
                float grid = spacetimeGrid(samplePos);
                float falloff = 1.0 / (rayT * rayT * 0.08 + 1.0);
                gridIntensity += grid * falloff * 0.08;
            }

            // Enhanced light blue spacetime grid with pulsing effect
            float pulse = 1.0 + sin(time * animSpeed * 4.0) * 0.3;
            vec3 gridColor = vec3(0.4, 0.8, 1.0) * gridIntensity * pulse;

            // Add rotating stars with fade and pulse effects
            float starIntensity = stars(rayDir);

            // Add individual star pulsing based on position
            vec3 starPos = rayDir * 100.0;
            float starPulse = 0.8 + 0.4 * sin(time * animSpeed * 2.0 + hash(floor(starPos)) * 6.28);

            // Create fade in/out effect for individual stars
            float starFade = 0.5 + 0.5 * sin(time * animSpeed * 1.5 + hash(floor(starPos * 1.1)) * 6.28);

            vec3 starColor = vec3(0.8, 0.9, 1.0) * starIntensity * starPulse * starFade * 1.5;

            col = gridColor + starColor;
        }

        // Add some atmospheric glow around the Mandelbulb
        float distToCenter = length(cameraPos + rayDir * t);
        float glow = exp(-distToCenter * 0.3) * 0.1;
        col += vec3(0.2, 0.4, 0.8) * glow;

        gl_FragColor = vec4(col, 1.0);
    }
`;

// Create and compile shaders
function createShader(type, source) {
    const shader = gl.createShader(type);
    gl.shaderSource(shader, source);
    gl.compileShader(shader);
    if (!gl.getShaderParameter(shader, gl.COMPILE_STATUS)) {
        console.error(gl.getShaderInfoLog(shader));
    }
    return shader;
}

const program = gl.createProgram();
gl.attachShader(program, createShader(gl.VERTEX_SHADER, vertexShaderSource));
gl.attachShader(program, createShader(gl.FRAGMENT_SHADER, fragmentShaderSource));
gl.linkProgram(program);
gl.useProgram(program);

// Set up buffers and attributes
const vertices = new Float32Array([-1,-1, 1,-1, -1,1, 1,1]);
const buffer = gl.createBuffer();
gl.bindBuffer(gl.ARRAY_BUFFER, buffer);
gl.bufferData(gl.ARRAY_BUFFER, vertices, gl.STATIC_DRAW);

const position = gl.getAttribLocation(program, 'position');
gl.enableVertexAttribArray(position);
gl.vertexAttribPointer(position, 2, gl.FLOAT, false, 0, 0);

// Set up uniforms
const uniforms = {
    resolution: gl.getUniformLocation(program, 'resolution'),
    cameraPos: gl.getUniformLocation(program, 'cameraPos'),
    cameraRot: gl.getUniformLocation(program, 'cameraRot'),
    time: gl.getUniformLocation(program, 'time'),
    gravityStrength: gl.getUniformLocation(program, 'gravityStrength'),
    gridDensity: gl.getUniformLocation(program, 'gridDensity'),
    spacetimeWarp: gl.getUniformLocation(program, 'spacetimeWarp'),
    starField: gl.getUniformLocation(program, 'starField'),
    animSpeed: gl.getUniformLocation(program, 'animSpeed')
};

// Input handling
const keys = new Set();
window.addEventListener('keydown', e => {
    const key = e.key.toLowerCase();
    keys.add(key);
    if (key === '+' || key === '=') {
        quality = Math.min(1.0, quality + 0.1);
        document.getElementById('qualityValue').textContent = Math.round(quality * 100);
        resize();
    }
    if (key === '-') {
        quality = Math.max(0.1, quality - 0.1);
        document.getElementById('qualityValue').textContent = Math.round(quality * 100);
        resize();
    }
});
window.addEventListener('keyup', e => keys.delete(e.key.toLowerCase()));

let lastMouseMove = 0;
canvas.addEventListener('mousemove', e => {
    const now = performance.now();
    if (now - lastMouseMove > 16 && (e.buttons & 1)) {
        cameraRot[0] += e.movementX * 0.01;
        cameraRot[1] = Math.max(-Math.PI/2, Math.min(Math.PI/2, cameraRot[1] - e.movementY * 0.01));
        lastMouseMove = now;
    }
});

let lastRender = 0;
function render(now) {
    if (now - lastRender >= 16) {
        const speed = 0.1;
        if (keys.has('w')) {
            cameraPos[0] += Math.sin(cameraRot[0]) * speed;
            cameraPos[2] += Math.cos(cameraRot[0]) * speed;
        }
        if (keys.has('s')) {
            cameraPos[0] -= Math.sin(cameraRot[0]) * speed;
            cameraPos[2] -= Math.cos(cameraRot[0]) * speed;
        }
        if (keys.has('a')) {
            cameraPos[0] -= Math.cos(cameraRot[0]) * speed;
            cameraPos[2] += Math.sin(cameraRot[0]) * speed;
        }
        if (keys.has('d')) {
            cameraPos[0] += Math.cos(cameraRot[0]) * speed;
            cameraPos[2] -= Math.sin(cameraRot[0]) * speed;
        }
        if (keys.has('q')) cameraPos[1] += speed;
        if (keys.has('e')) cameraPos[1] -= speed;

        time = now * 0.001;

        gl.uniform2f(uniforms.resolution, canvas.width, canvas.height);
        gl.uniform3f(uniforms.cameraPos, ...cameraPos);
        gl.uniform2f(uniforms.cameraRot, ...cameraRot);
        gl.uniform1f(uniforms.time, time);
        gl.uniform1f(uniforms.gravityStrength, gravityStrength);
        gl.uniform1f(uniforms.gridDensity, gridDensity);
        gl.uniform1f(uniforms.spacetimeWarp, spacetimeWarp);
        gl.uniform1f(uniforms.starField, starField);
        gl.uniform1f(uniforms.animSpeed, animSpeed);

        gl.drawArrays(gl.TRIANGLE_STRIP, 0, 4);
        lastRender = now;
    }
    requestAnimationFrame(render);
}

requestAnimationFrame(render);
</script>
</body>
</html>
