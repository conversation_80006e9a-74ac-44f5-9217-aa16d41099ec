<!DOCTYPE html>
<html>
<head>
    <title>Mandelbulb with Gravitational Field</title>
    <style>
        body { margin: 0; overflow: hidden; background: black; }
        canvas { width: 100vw; height: 100vh; display: block; }
        .controls {
            position: fixed;
            top: 10px;
            left: 10px;
            color: white;
            font-family: monospace;
            background: rgba(0,0,0,0.7);
            padding: 15px;
            border-radius: 5px;
            backdrop-filter: blur(5px);
        }
        .physics-controls {
            position: fixed;
            bottom: 10px;
            left: 10px;
            color: white;
            background: rgba(0,0,0,0.7);
            padding: 15px;
            border-radius: 5px;
            backdrop-filter: blur(5px);
        }
        .slider-container {
            margin: 8px 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .slider-container label {
            min-width: 120px;
            font-size: 12px;
        }
        input[type="range"] {
            width: 150px;
        }
        #quality {
            position: fixed;
            top: 10px;
            right: 10px;
            color: white;
            background: rgba(0,0,0,0.7);
            padding: 10px;
            border-radius: 5px;
        }
    </style>
</head>
<body>
<div class="controls">
    <strong>🌌 Gravitational Mandelbulb</strong><br>
    WASD - Move | Mouse - Look | Q/E - Up/Down<br>
    +/- Keys: Adjust Quality
</div>
<div id="quality">Quality: <span id="qualityValue">60</span>%</div>
<div class="physics-controls">
    <div class="slider-container">
        <label>🌟 Gravity Strength:</label>
        <input type="range" id="gravityStrength" min="0" max="100" value="50">
    </div>
    <div class="slider-container">
        <label>🌀 Lensing Effect:</label>
        <input type="range" id="lensingEffect" min="0" max="100" value="70">
    </div>
    <div class="slider-container">
        <label>⭐ Star Density:</label>
        <input type="range" id="starDensity" min="10" max="200" value="80">
    </div>
    <div class="slider-container">
        <label>🔄 Rotation Speed:</label>
        <input type="range" id="rotationSpeed" min="0" max="100" value="30">
    </div>
    <div class="slider-container">
        <label>💫 Distortion:</label>
        <input type="range" id="distortion" min="0" max="100" value="40">
    </div>
</div>
<canvas id="canvas"></canvas>
<script>
const canvas = document.getElementById('canvas');
const gl = canvas.getContext('webgl');

// Camera and quality state
let cameraPos = [0.0, 0.0, -5.0];
let cameraRot = [0.0, 0.0];
let quality = 0.6;

// Physics parameters
let time = 0;
let gravityStrength = 0.5;
let lensingEffect = 0.7;
let starDensity = 0.8;
let rotationSpeed = 0.3;
let distortion = 0.4;

// Update physics parameters from sliders
document.getElementById('gravityStrength').addEventListener('input', (e) => {
    gravityStrength = e.target.value / 100;
});
document.getElementById('lensingEffect').addEventListener('input', (e) => {
    lensingEffect = e.target.value / 100;
});
document.getElementById('starDensity').addEventListener('input', (e) => {
    starDensity = e.target.value / 100;
});
document.getElementById('rotationSpeed').addEventListener('input', (e) => {
    rotationSpeed = e.target.value / 100;
});
document.getElementById('distortion').addEventListener('input', (e) => {
    distortion = e.target.value / 100;
});

// Resize handler
function resize() {
    const width = window.innerWidth * quality;
    const height = window.innerHeight * quality;
    canvas.style.width = window.innerWidth + 'px';
    canvas.style.height = window.innerHeight + 'px';
    canvas.width = width;
    canvas.height = height;
    gl.viewport(0, 0, width, height);
}
window.addEventListener('resize', resize);
resize();

const vertexShaderSource = `
    attribute vec2 position;
    void main() {
        gl_Position = vec4(position, 0.0, 1.0);
    }
`;
